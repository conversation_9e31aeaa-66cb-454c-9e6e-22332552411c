/**
 * Supabase Real-time Connection Management Store
 *
 * Centralized Zustand store for managing Supabase real-time subscriptions
 * to prevent connection leaks and ensure efficient resource utilization.
 *
 * Features:
 * - Global subscription deduplication
 * - Automatic connection cleanup
 * - Development-time connection monitoring
 * - Production-ready connection tracking
 *
 * <AUTHOR> Augster
 * @version 1.0 - Initial Implementation (January 2025)
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type {
  ManagedChannel,
  SupabaseStoreState,
  SupabaseStoreActions,
} from '@/types/supabase-realtime';

// Use imported types from supabase-realtime.ts

// Initial state
const initialState: SupabaseStoreState = {
  channels: {},
  connectionCount: 0,
  connectionHistory: [],
  maxConnections: 180, // Conservative limit below Supabase's 200
  inactivityTimeout: 5 * 60 * 1000, // 5 minutes
  enableLogging: process.env.NODE_ENV === 'development',
};

// Create the store
export const useSupabaseStore = create<
  SupabaseStoreState & SupabaseStoreActions
>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Core channel management
      addChannel: (key, channel, metadata) => {
        const state = get();
        const now = Date.now();

        // Check if channel already exists
        if (state.channels[key]) {
          // Increment reference count for existing channel
          get().incrementRef(key);
          get().logConnection('reused', key, metadata);
          return;
        }

        // Check connection limit
        if (state.connectionCount >= state.maxConnections) {
          console.warn(
            `⚠️ Supabase connection limit reached (${state.maxConnections}). ` +
              'Consider removing inactive channels or increasing the limit.'
          );

          // Attempt to clean up inactive channels
          get().removeInactiveChannels();

          // If still at limit, reject new connection
          if (get().connectionCount >= state.maxConnections) {
            console.error(
              '❌ Cannot create new Supabase channel: connection limit exceeded'
            );
            return;
          }
        }

        // Create managed channel
        const managedChannel: ManagedChannel = {
          channel,
          metadata: {
            ...metadata,
            createdAt: now,
            lastActivity: now,
          },
          refCount: 1,
        };

        set((state) => ({
          channels: {
            ...state.channels,
            [key]: managedChannel,
          },
          connectionCount: state.connectionCount + 1,
        }));

        get().logConnection('created', key, metadata);

        if (state.enableLogging) {
          console.log(`✅ Supabase channel created: ${key}`, {
            table: metadata.table,
            filter: metadata.filter,
            totalConnections: get().connectionCount,
          });
        }
      },

      removeChannel: (key, force = false) => {
        const state = get();
        const managedChannel = state.channels[key];

        if (!managedChannel) {
          if (state.enableLogging) {
            console.warn(`⚠️ Attempted to remove non-existent channel: ${key}`);
          }
          return;
        }

        // Decrement reference count unless forced
        if (!force && managedChannel.refCount > 1) {
          get().decrementRef(key);
          return;
        }

        // Unsubscribe from the channel
        try {
          managedChannel.channel.unsubscribe();
        } catch (error) {
          console.error(`❌ Error unsubscribing from channel ${key}:`, error);
        }

        // Remove from store
        set((state) => {
          const { [key]: removed, ...remainingChannels } = state.channels;
          return {
            channels: remainingChannels,
            connectionCount: Math.max(0, state.connectionCount - 1),
          };
        });

        get().logConnection('removed', key);

        if (state.enableLogging) {
          console.log(`🗑️ Supabase channel removed: ${key}`, {
            totalConnections: get().connectionCount,
          });
        }
      },

      getChannel: (key) => {
        const managedChannel = get().channels[key];
        if (managedChannel) {
          // Update last activity
          set((state) => ({
            channels: {
              ...state.channels,
              [key]: {
                ...managedChannel,
                metadata: {
                  ...managedChannel.metadata,
                  lastActivity: Date.now(),
                },
              },
            },
          }));
          return managedChannel.channel;
        }
        return undefined;
      },

      hasChannel: (key) => {
        return key in get().channels;
      },

      // Reference counting
      incrementRef: (key) => {
        const state = get();
        const managedChannel = state.channels[key];

        if (managedChannel) {
          set((state) => ({
            channels: {
              ...state.channels,
              [key]: {
                ...managedChannel,
                refCount: managedChannel.refCount + 1,
                metadata: {
                  ...managedChannel.metadata,
                  lastActivity: Date.now(),
                },
              },
            },
          }));
        }
      },

      decrementRef: (key) => {
        const state = get();
        const managedChannel = state.channels[key];

        if (managedChannel && managedChannel.refCount > 0) {
          const newRefCount = managedChannel.refCount - 1;

          if (newRefCount === 0) {
            // Remove channel when no more references
            get().removeChannel(key, true);
          } else {
            set((state) => ({
              channels: {
                ...state.channels,
                [key]: {
                  ...managedChannel,
                  refCount: newRefCount,
                },
              },
            }));
          }
        }
      },

      // Bulk operations
      removeAllChannels: () => {
        const state = get();
        const channelKeys = Object.keys(state.channels);

        channelKeys.forEach((key) => {
          get().removeChannel(key, true);
        });

        if (state.enableLogging) {
          console.log(
            `🧹 Removed all Supabase channels (${channelKeys.length})`
          );
        }
      },

      removeChannelsByTenant: (tenantId) => {
        const state = get();
        const channelsToRemove = Object.entries(state.channels)
          .filter(
            ([, managedChannel]) =>
              managedChannel.metadata.tenantId === tenantId
          )
          .map(([key]) => key);

        channelsToRemove.forEach((key) => {
          get().removeChannel(key, true);
        });

        if (state.enableLogging) {
          console.log(
            `🧹 Removed ${channelsToRemove.length} channels for tenant: ${tenantId}`
          );
        }
      },

      removeInactiveChannels: (inactivityThreshold) => {
        const state = get();
        const threshold = inactivityThreshold || state.inactivityTimeout;
        const now = Date.now();

        const inactiveChannels = Object.entries(state.channels)
          .filter(
            ([, managedChannel]) =>
              now - managedChannel.metadata.lastActivity > threshold
          )
          .map(([key]) => key);

        inactiveChannels.forEach((key) => {
          get().removeChannel(key, true);
        });

        if (state.enableLogging && inactiveChannels.length > 0) {
          console.log(
            `🧹 Removed ${inactiveChannels.length} inactive channels`
          );
        }

        return inactiveChannels.length;
      },

      // Monitoring and debugging
      getConnectionStats: () => {
        const state = get();
        const channels = Object.values(state.channels);

        const byTable: Record<string, number> = {};
        const byTenant: Record<string, number> = {};
        let oldestConnection: number | null = null;

        channels.forEach((managedChannel) => {
          const { table, tenantId, createdAt } = managedChannel.metadata;

          byTable[table] = (byTable[table] || 0) + 1;

          if (tenantId) {
            byTenant[tenantId] = (byTenant[tenantId] || 0) + 1;
          }

          if (oldestConnection === null || createdAt < oldestConnection) {
            oldestConnection = createdAt;
          }
        });

        return {
          total: state.connectionCount,
          byTable,
          byTenant,
          oldestConnection,
        };
      },

      logConnection: (action, channelKey, metadata) => {
        const state = get();

        if (!state.enableLogging) return;

        const logEntry = {
          action,
          channelKey,
          timestamp: Date.now(),
          metadata,
        };

        set((state) => ({
          connectionHistory: [
            ...state.connectionHistory.slice(-99), // Keep last 100 entries
            logEntry,
          ],
        }));
      },

      clearHistory: () => {
        set({ connectionHistory: [] });
      },

      // Configuration
      setMaxConnections: (max) => {
        set({ maxConnections: max });
      },

      setInactivityTimeout: (timeout) => {
        set({ inactivityTimeout: timeout });
      },

      setLogging: (enabled) => {
        set({ enableLogging: enabled });
      },
    }),
    {
      name: 'supabase-store',
    }
  )
);

// Selector hooks for optimized re-renders
export const useSupabaseSelectors = {
  useConnectionCount: () => useSupabaseStore((state) => state.connectionCount),
  useConnectionStats: () =>
    useSupabaseStore((state) => state.getConnectionStats()),
  useHasChannel: (key: string) =>
    useSupabaseStore((state) => state.hasChannel(key)),
  useConnectionHistory: () =>
    useSupabaseStore((state) => state.connectionHistory),

  // Actions
  useSupabaseActions: () =>
    useSupabaseStore((state) => ({
      addChannel: state.addChannel,
      removeChannel: state.removeChannel,
      getChannel: state.getChannel,
      hasChannel: state.hasChannel,
      removeAllChannels: state.removeAllChannels,
      removeChannelsByTenant: state.removeChannelsByTenant,
      removeInactiveChannels: state.removeInactiveChannels,
      getConnectionStats: state.getConnectionStats,
      setMaxConnections: state.setMaxConnections,
      setInactivityTimeout: state.setInactivityTimeout,
      setLogging: state.setLogging,
    })),
};

// Development helper for debugging connections
export const debugSupabaseConnections = () => {
  if (process.env.NODE_ENV === 'development') {
    const store = useSupabaseStore.getState();
    console.group('🔍 Supabase Connection Debug');
    console.log('Total connections:', store.connectionCount);
    console.log('Active channels:', Object.keys(store.channels));
    console.log('Connection stats:', store.getConnectionStats());
    console.log('Recent history:', store.connectionHistory.slice(-10));
    console.groupEnd();
  }
};
