# Supabase Connection Optimization Guide

## Overview

This guide provides comprehensive instructions for implementing and using the Supabase real-time connection management system to prevent connection leaks and optimize resource usage.

## Quick Start

### 1. Add the Lifecycle Provider to Your App Root

```tsx
// app/layout.tsx or pages/_app.tsx
import { SupabaseLifecycleProvider } from '@/components/SupabaseLifecycleProvider';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body>
        <SupabaseLifecycleProvider>
          {/* Your existing providers */}
          {children}
        </SupabaseLifecycleProvider>
      </body>
    </html>
  );
}
```

### 2. Use Enhanced Real-time Hooks

The existing `useRealtimeQuery` hook now automatically uses the centralized connection management:

```tsx
// No changes needed to existing code!
const { data: tickets } = useRealtimeQuery(
  ['tickets', tenantId],
  () => fetchTickets(tenantId),
  'tickets',
  {
    filter: `tenant_id=eq.${tenantId}`,
    queryOptions: { enabled: !!tenantId }
  }
);
```

### 3. Add Development Monitoring (Optional)

```tsx
// Add to your development layout
import { SupabaseConnectionStatus, SupabaseLifecycleDebugPanel } from '@/components/SupabaseLifecycleProvider';

export default function DevLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <>
          <SupabaseConnectionStatus />
          <SupabaseLifecycleDebugPanel />
        </>
      )}
    </>
  );
}
```

## Key Features

### ✅ Automatic Connection Deduplication
- Prevents duplicate subscriptions for the same table/filter combination
- Uses reference counting to share connections across components
- Automatically cleans up when no components are using a connection

### ✅ Lifecycle Management
- **beforeunload**: Cleans up all connections when page is refreshed or closed
- **visibilitychange**: Cleans up when tab becomes hidden
- **inactivity**: Removes inactive connections after 5 minutes of no activity
- **network**: Handles online/offline events

### ✅ Development Tools
- Real-time connection monitoring overlay
- Debug panel with manual cleanup controls
- Console debugging utilities
- Performance metrics and leak detection

### ✅ Production Monitoring
- Connection count alerts
- Performance tracking
- Automatic cleanup of stale connections
- Memory usage monitoring

## Configuration Options

### Lifecycle Configuration

```tsx
<SupabaseLifecycleProvider
  config={{
    enableBeforeUnload: true,
    enableVisibilityChange: true,
    enableInactivityTimeout: true,
    inactivityTimeout: 5 * 60 * 1000, // 5 minutes
    activityEvents: ['mousemove', 'keydown', 'click', 'scroll'],
    enableNetworkMonitoring: true,
  }}
>
  {children}
</SupabaseLifecycleProvider>
```

### Store Configuration

```tsx
// Access store configuration
import { useSupabaseSelectors } from '@/stores/supabase-store';

const { setMaxConnections, setInactivityTimeout, setLogging } = 
  useSupabaseSelectors.useSupabaseActions();

// Configure limits
setMaxConnections(150); // Conservative limit
setInactivityTimeout(10 * 60 * 1000); // 10 minutes
setLogging(true); // Enable detailed logging
```

## Debugging and Monitoring

### Development Console Tools

```javascript
// Available in development mode
window.supabaseDebug.debug(); // Print comprehensive debug report
window.supabaseDebug.analyze(); // Analyze connection patterns
window.supabaseDebug.cleanup(); // Force cleanup all connections
window.supabaseDebug.cleanupInactive(); // Remove inactive connections

// Lifecycle tools
window.supabaseLifecycle.debugConnections(); // Debug current connections
window.supabaseLifecycle.forceCleanup(); // Manual cleanup
window.supabaseLifecycle.simulateOffline(); // Test offline handling
```

### Monitoring Hooks

```tsx
// Track activity in components
import { useSupabaseActivityTracker } from '@/components/SupabaseLifecycleProvider';

function MyComponent() {
  const { trackActivity } = useSupabaseActivityTracker();
  
  const handleUserAction = () => {
    // Reset inactivity timer
    trackActivity();
    // ... handle action
  };
}

// Register cleanup handlers
import { useSupabaseCleanupHandler } from '@/components/SupabaseLifecycleProvider';

function MyComponent() {
  useSupabaseCleanupHandler(() => {
    // Custom cleanup logic
    console.log('Component cleanup');
  });
}
```

### Production Monitoring

```tsx
// Get connection statistics
import { useSupabaseSelectors } from '@/stores/supabase-store';

function ConnectionMonitor() {
  const stats = useSupabaseSelectors.useConnectionStats();
  
  // Alert if approaching limits
  useEffect(() => {
    if (stats.total > 150) {
      // Send alert to monitoring service
      console.warn('High connection count:', stats.total);
    }
  }, [stats.total]);
}
```

## Best Practices

### 1. Component Design
- Use `useRealtimeQuery` instead of manual subscriptions
- Ensure components properly unmount to trigger cleanup
- Avoid creating subscriptions in render functions

### 2. Connection Optimization
- Use specific filters to reduce data transfer
- Combine related subscriptions when possible
- Implement proper error boundaries

### 3. Testing
- Test component unmounting scenarios
- Verify cleanup during navigation
- Monitor connection counts during development

### 4. Production Deployment
- Set conservative connection limits (150-180)
- Enable monitoring and alerting
- Implement graceful degradation for connection failures

## Troubleshooting

### High Connection Count
1. Check for components not properly unmounting
2. Look for duplicate subscriptions (same table/filter)
3. Review filter specificity
4. Use debug tools to identify patterns

```javascript
// Debug high connection count
window.supabaseDebug.debug();
window.supabaseDebug.analyze();
```

### Connection Leaks
1. Verify lifecycle provider is at app root
2. Check for manual subscription creation
3. Review component cleanup logic
4. Monitor stale connections

```javascript
// Find stale connections
window.supabaseDebug.cleanupInactive(30000); // Remove connections inactive > 30s
```

### Performance Issues
1. Monitor memory usage in debug overlay
2. Check for excessive re-subscriptions
3. Review filter efficiency
4. Consider connection pooling for high-traffic scenarios

## Migration Guide

### From Manual Subscriptions

**Before:**
```tsx
useEffect(() => {
  const channel = supabase
    .channel('tickets')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'tickets' }, handler)
    .subscribe();
    
  return () => {
    supabase.removeChannel(channel);
  };
}, []);
```

**After:**
```tsx
// Automatically managed!
const { data } = useRealtimeQuery(
  ['tickets'],
  () => fetchTickets(),
  'tickets'
);
```

### Gradual Rollout

1. Add lifecycle provider to app root
2. Replace manual subscriptions with `useRealtimeQuery` one by one
3. Monitor connection counts during migration
4. Enable debug tools to verify proper cleanup

## Performance Metrics

The system tracks several key metrics:

- **Connection Count**: Total active connections
- **Connection Age**: How long connections have been active
- **Reference Count**: How many components share each connection
- **Memory Usage**: JavaScript heap usage (when available)
- **Activity Tracking**: Last activity timestamp per connection

## Alerts and Thresholds

Default alert thresholds:
- **Warning**: 100 connections
- **Critical**: 150 connections
- **Stale**: 30 minutes of inactivity
- **Cleanup**: 5 minutes of global inactivity

## Support

For issues or questions:
1. Check the debug console output
2. Use the development tools for analysis
3. Review connection patterns and recommendations
4. Monitor the connection status overlay

The system is designed to be self-healing and should automatically manage connections in most scenarios.
