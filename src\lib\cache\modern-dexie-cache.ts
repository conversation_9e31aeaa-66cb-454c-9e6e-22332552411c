/**
 * Modern Dexie Cache - 2025 Best Practices
 *
 * Uses useLiveQuery for reactive queries and "update local cache first" pattern
 * Integrates seamlessly with React Query's official IndexedDB persistence
 */

import Dexie, { Table } from 'dexie';

// Simplified message interface for reactive queries
export interface CachedMessage {
  id: string;
  tenant_id: string;
  ticket_id: string;
  content: string;
  author_id: string;
  author_name: string;
  author_avatar: string;
  created_at: string;
  updated_at: string;
  attachments: Array<{
    id: string;
    name: string;
    size: number;
    type: string;
    url?: string;
  }>;
  // Minimal metadata for performance
  cached_at: number;
  is_expanded: boolean; // Track if cached during expansion
}

// Input message interface for caching operations
export interface InputMessageData {
  id: string;
  content: string;
  author_name: string;
  author_avatar?: string;
  created_at: string;
  updated_at?: string;
  attachments?: Array<{
    id: string;
    name: string;
    size: number;
    type: string;
    url?: string;
  }>;
}

// Modern Dexie database with optimized schema
class ModernCacheDatabase extends Dexie {
  messages!: Table<CachedMessage>;

  constructor() {
    super('TicketingModernCache');

    this.version(1).stores({
      // Optimized indexes for common query patterns
      messages: '&id, tenant_id, ticket_id, created_at, [tenant_id+ticket_id]',
    });
  }
}

export const modernCacheDB = new ModernCacheDatabase();

/**
 * Modern cache operations using "update local cache first" pattern
 */
export class ModernMessageCache {
  /**
   * Update local cache first, then let React Query sync with server
   * This is the 2025 best practice pattern
   */
  static async updateLocalFirst(
    tenantId: string,
    ticketId: string,
    messages: Partial<CachedMessage>[]
  ): Promise<void> {
    const now = Date.now();

    const cachedMessages: CachedMessage[] = messages.map((msg) => ({
      id: msg.id!,
      tenant_id: tenantId,
      ticket_id: ticketId,
      content: msg.content || '',
      author_id: msg.author_id || '',
      author_name: msg.author_name || '',
      author_avatar: msg.author_avatar || '',
      created_at: msg.created_at || new Date().toISOString(),
      updated_at: msg.updated_at || new Date().toISOString(),
      attachments: msg.attachments || [],
      cached_at: now,
      is_expanded: msg.is_expanded || false,
    }));

    // Bulk upsert for performance
    await modernCacheDB.messages.bulkPut(cachedMessages);

    console.log(
      `📦 Updated local cache first: ${messages.length} messages for ticket ${ticketId}`
    );
  }

  /**
   * Cache initial messages (first + last pattern)
   */
  static async cacheInitialMessages(
    tenantId: string,
    ticketId: string,
    messages: InputMessageData[]
  ): Promise<void> {
    if (messages.length === 0) return;

    // Cache first and last messages only (matches UI pattern)
    const initialMessages =
      messages.length <= 2
        ? messages
        : [messages[0], messages[messages.length - 1]];

    await this.updateLocalFirst(
      tenantId,
      ticketId,
      initialMessages.map((msg) => {
        if (!msg) throw new Error('Message is undefined');
        return {
          id: msg.id,
          content: msg.content,
          author_id: msg.author_name, // Use author_name as author_id
          author_name: msg.author_name,
          author_avatar: msg.author_avatar || '',
          created_at: msg.created_at,
          updated_at: msg.updated_at || msg.created_at,
          attachments: msg.attachments || [],
          is_expanded: false,
        };
      })
    );
  }

  /**
   * Cache expanded messages (all messages when user clicks "2")
   */
  static async cacheExpandedMessages(
    tenantId: string,
    ticketId: string,
    allMessages: InputMessageData[]
  ): Promise<void> {
    await this.updateLocalFirst(
      tenantId,
      ticketId,
      allMessages.map((msg) => ({
        id: msg.id,
        content: msg.content,
        author_id: msg.author_name, // Use author_name as author_id
        author_name: msg.author_name,
        author_avatar: msg.author_avatar || '',
        created_at: msg.created_at,
        updated_at: msg.updated_at || msg.created_at,
        attachments: msg.attachments || [],
        is_expanded: true,
      }))
    );
  }

  /**
   * Incremental sync - minimal 2025 approach (only fetch changes since timestamp)
   */
  static async syncChanges(
    _tenantId: string,
    ticketId: string,
    sinceTimestamp: number
  ): Promise<void> {
    // Clean expired cache first (30-minute expiration)
    await this.cleanupExpiredCache();

    // In real implementation, this would fetch only changed messages from server
    // For now, we'll just update the sync timestamp
    console.log(
      `🔄 Incremental sync for ticket ${ticketId} since ${new Date(sinceTimestamp).toISOString()}`
    );
  }

  /**
   * 30-minute cache expiration - minimal cleanup
   */
  static async cleanupExpiredCache(): Promise<void> {
    const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000;

    const deletedCount = await modernCacheDB.messages
      .where('cached_at')
      .below(thirtyMinutesAgo)
      .delete();

    if (deletedCount > 0) {
      console.log(`🧹 Cleaned up ${deletedCount} expired cache entries`);
    }
  }

  /**
   * Add new message (real-time updates)
   */
  static async addMessage(
    tenantId: string,
    ticketId: string,
    message: InputMessageData
  ): Promise<void> {
    await this.updateLocalFirst(tenantId, ticketId, [
      {
        id: message.id,
        content: message.content,
        author_id: message.author_name, // Use author_name as author_id
        author_name: message.author_name,
        author_avatar: message.author_avatar || '',
        created_at: message.created_at,
        updated_at: message.updated_at || message.created_at,
        attachments: message.attachments || [],
        is_expanded: true, // New messages are always visible
      },
    ]);
  }

  /**
   * Update existing message
   */
  static async updateMessage(
    messageId: string,
    updates: Partial<CachedMessage>
  ): Promise<void> {
    await modernCacheDB.messages.update(messageId, {
      ...updates,
      updated_at: new Date().toISOString(),
    });
  }

  /**
   * Delete message
   */
  static async deleteMessage(messageId: string): Promise<void> {
    await modernCacheDB.messages.delete(messageId);
  }

  /**
   * Clear cache for a specific ticket
   */
  static async clearTicketCache(
    tenantId: string,
    ticketId: string
  ): Promise<void> {
    await modernCacheDB.messages
      .where('[tenant_id+ticket_id]')
      .equals([tenantId, ticketId])
      .delete();
  }

  /**
   * Clear all cache for a tenant
   */
  static async clearTenantCache(tenantId: string): Promise<void> {
    await modernCacheDB.messages.where('tenant_id').equals(tenantId).delete();
  }

  /**
   * Smart cleanup - remove old entries to manage storage
   */
  static async performSmartCleanup(tenantId: string): Promise<void> {
    const oneWeekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

    await modernCacheDB.messages
      .where('tenant_id')
      .equals(tenantId)
      .and((msg) => msg.cached_at < oneWeekAgo)
      .delete();

    console.log(`🧹 Performed smart cleanup for tenant: ${tenantId}`);
  }
}

/**
 * Export for easy migration from old cache
 */
export { modernCacheDB as cacheDB, ModernMessageCache as MessageCache };
