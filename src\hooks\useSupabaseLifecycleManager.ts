/**
 * Supabase Connection Lifecycle Management Hook
 * 
 * Global hook that manages the lifecycle of all Supabase real-time connections
 * to prevent connection leaks and ensure proper cleanup during various browser events.
 * 
 * Features:
 * - beforeunload event cleanup
 * - Visibility change handling
 * - Inactivity-based disconnection
 * - Network status monitoring
 * - Custom cleanup handlers
 * 
 * <AUTHOR> Augster
 * @version 1.0 - Initial Implementation (January 2025)
 */

import { useEffect, useRef, useCallback } from 'react';
import { useSupabaseSelectors } from '@/stores/supabase-store';
import type { LifecycleConfig, LifecycleEvent } from '@/types/supabase-realtime';

// Default configuration
const DEFAULT_CONFIG: LifecycleConfig = {
  enableBeforeUnload: true,
  enableVisibilityChange: true,
  enableInactivityTimeout: true,
  inactivityTimeout: 5 * 60 * 1000, // 5 minutes
  activityEvents: ['mousemove', 'keydown', 'click', 'scroll', 'touchstart'],
  enableNetworkMonitoring: true,
  customCleanupHandlers: [],
};

/**
 * Global lifecycle management hook for Supabase connections
 * 
 * This hook should be used once at the application root level to ensure
 * proper cleanup of all Supabase connections during various lifecycle events.
 * 
 * @param config - Configuration options for lifecycle management
 */
export function useSupabaseLifecycleManager(config: Partial<LifecycleConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const { removeAllChannels, removeInactiveChannels, getConnectionStats } = 
    useSupabaseSelectors.useSupabaseActions();
  
  // Refs for managing timers and state
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const isOnlineRef = useRef<boolean>(navigator.onLine);
  const cleanupHandlersRef = useRef<Set<() => void>>(new Set());

  /**
   * Logs lifecycle events for debugging
   */
  const logLifecycleEvent = useCallback((event: LifecycleEvent, details?: Record<string, unknown>) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Supabase Lifecycle Event: ${event}`, details);
    }
  }, []);

  /**
   * Performs complete cleanup of all connections
   */
  const performFullCleanup = useCallback((reason: string) => {
    logLifecycleEvent('custom', { action: 'full_cleanup', reason });
    
    // Remove all Supabase channels
    removeAllChannels();
    
    // Execute custom cleanup handlers
    cleanupHandlersRef.current.forEach(handler => {
      try {
        handler();
      } catch (error) {
        console.error('Error in custom cleanup handler:', error);
      }
    });
    
    // Clear inactivity timer
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🧹 Full Supabase cleanup completed: ${reason}`);
    }
  }, [removeAllChannels, logLifecycleEvent]);

  /**
   * Resets the inactivity timer
   */
  const resetInactivityTimer = useCallback(() => {
    if (!finalConfig.enableInactivityTimeout) return;
    
    lastActivityRef.current = Date.now();
    
    // Clear existing timer
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }
    
    // Set new timer
    inactivityTimerRef.current = setTimeout(() => {
      logLifecycleEvent('inactivity', { 
        timeout: finalConfig.inactivityTimeout,
        lastActivity: lastActivityRef.current 
      });
      
      // Remove inactive channels instead of all channels
      const removedCount = removeInactiveChannels(finalConfig.inactivityTimeout);
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏰ Inactivity cleanup: removed ${removedCount} channels`);
      }
    }, finalConfig.inactivityTimeout);
  }, [finalConfig.enableInactivityTimeout, finalConfig.inactivityTimeout, removeInactiveChannels, logLifecycleEvent]);

  /**
   * Handles activity events to reset inactivity timer
   */
  const handleActivity = useCallback(() => {
    resetInactivityTimer();
  }, [resetInactivityTimer]);

  /**
   * Handles beforeunload event
   */
  const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
    logLifecycleEvent('beforeunload');
    performFullCleanup('beforeunload');
    
    // Note: We don't prevent the unload, just clean up
    // Some browsers may ignore this cleanup due to timing
  }, [performFullCleanup, logLifecycleEvent]);

  /**
   * Handles visibility change events
   */
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'hidden') {
      logLifecycleEvent('visibilitychange', { state: 'hidden' });
      
      // Clean up connections when tab becomes hidden
      performFullCleanup('visibility_hidden');
    } else if (document.visibilityState === 'visible') {
      logLifecycleEvent('visibilitychange', { state: 'visible' });
      
      // Reset activity timer when tab becomes visible
      resetInactivityTimer();
    }
  }, [performFullCleanup, resetInactivityTimer, logLifecycleEvent]);

  /**
   * Handles network status changes
   */
  const handleOnline = useCallback(() => {
    isOnlineRef.current = true;
    logLifecycleEvent('network-online');
    
    // Reset activity timer when coming back online
    resetInactivityTimer();
  }, [resetInactivityTimer, logLifecycleEvent]);

  const handleOffline = useCallback(() => {
    isOnlineRef.current = false;
    logLifecycleEvent('network-offline');
    
    // Clean up connections when going offline
    performFullCleanup('network_offline');
  }, [performFullCleanup, logLifecycleEvent]);

  /**
   * Handles focus events
   */
  const handleFocus = useCallback(() => {
    logLifecycleEvent('focus');
    resetInactivityTimer();
  }, [resetInactivityTimer, logLifecycleEvent]);

  const handleBlur = useCallback(() => {
    logLifecycleEvent('blur');
    // Don't clean up on blur, just log the event
  }, [logLifecycleEvent]);

  // Set up event listeners
  useEffect(() => {
    // beforeunload cleanup
    if (finalConfig.enableBeforeUnload) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    // Visibility change cleanup
    if (finalConfig.enableVisibilityChange) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
    }

    // Activity event listeners for inactivity timeout
    if (finalConfig.enableInactivityTimeout) {
      finalConfig.activityEvents.forEach(eventType => {
        document.addEventListener(eventType, handleActivity, { passive: true });
      });
      
      // Initialize inactivity timer
      resetInactivityTimer();
    }

    // Network status monitoring
    if (finalConfig.enableNetworkMonitoring) {
      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);
    }

    // Focus/blur events
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      // Clean up event listeners
      if (finalConfig.enableBeforeUnload) {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      }

      if (finalConfig.enableVisibilityChange) {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      }

      if (finalConfig.enableInactivityTimeout) {
        finalConfig.activityEvents.forEach(eventType => {
          document.removeEventListener(eventType, handleActivity);
        });
        
        if (inactivityTimerRef.current) {
          clearTimeout(inactivityTimerRef.current);
        }
      }

      if (finalConfig.enableNetworkMonitoring) {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      }

      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [
    finalConfig,
    handleBeforeUnload,
    handleVisibilityChange,
    handleActivity,
    handleOnline,
    handleOffline,
    handleFocus,
    handleBlur,
    resetInactivityTimer,
  ]);

  // Expose utilities for manual control
  return {
    /**
     * Manually trigger full cleanup
     */
    forceCleanup: (reason = 'manual') => performFullCleanup(reason),
    
    /**
     * Reset the inactivity timer manually
     */
    resetInactivityTimer,
    
    /**
     * Add a custom cleanup handler
     */
    addCleanupHandler: (handler: () => void) => {
      cleanupHandlersRef.current.add(handler);
      return () => cleanupHandlersRef.current.delete(handler);
    },
    
    /**
     * Get current connection statistics
     */
    getStats: getConnectionStats,
    
    /**
     * Check if currently online
     */
    isOnline: () => isOnlineRef.current,
    
    /**
     * Get last activity timestamp
     */
    getLastActivity: () => lastActivityRef.current,
    
    /**
     * Get current configuration
     */
    getConfig: () => finalConfig,
  };
}

/**
 * Simplified hook for basic lifecycle management
 * 
 * This is a convenience hook that uses sensible defaults for most applications.
 * Use the full useSupabaseLifecycleManager for more control.
 */
export function useBasicSupabaseLifecycle() {
  return useSupabaseLifecycleManager({
    enableBeforeUnload: true,
    enableVisibilityChange: true,
    enableInactivityTimeout: true,
    inactivityTimeout: 5 * 60 * 1000, // 5 minutes
    enableNetworkMonitoring: true,
  });
}
