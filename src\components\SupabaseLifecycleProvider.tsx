/**
 * Supabase Lifecycle Provider Component
 *
 * Provider component that manages the global lifecycle of Supabase connections.
 * This should be placed at the root of your application to ensure proper
 * connection cleanup across all components.
 *
 * <AUTHOR> Augster
 * @version 1.0 - Initial Implementation (January 2025)
 */

'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useSupabaseLifecycleManager } from '@/hooks/useSupabaseLifecycleManager';
import type { LifecycleConfig } from '@/types/supabase-realtime';

// Context type for the lifecycle manager
interface SupabaseLifecycleContextType {
  forceCleanup: (reason?: string) => void;
  resetInactivityTimer: () => void;
  addCleanupHandler: (handler: () => void) => () => void;
  getStats: () => any;
  isOnline: () => boolean;
  getLastActivity: () => number;
  getConfig: () => LifecycleConfig;
}

// Create context
const SupabaseLifecycleContext =
  createContext<SupabaseLifecycleContextType | null>(null);

// Provider props
interface SupabaseLifecycleProviderProps {
  children: ReactNode;
  config?: Partial<LifecycleConfig>;
  enableDevTools?: boolean;
}

/**
 * Provider component for Supabase lifecycle management
 *
 * @param children - Child components
 * @param config - Lifecycle configuration options
 * @param enableDevTools - Whether to enable development tools
 */
export function SupabaseLifecycleProvider({
  children,
  config = {},
  enableDevTools = process.env.NODE_ENV === 'development',
}: SupabaseLifecycleProviderProps) {
  // Initialize lifecycle manager
  const lifecycleManager = useSupabaseLifecycleManager(config);

  // Add development tools to window object
  React.useEffect(() => {
    if (enableDevTools && typeof window !== 'undefined') {
      (window as any).supabaseLifecycle = {
        ...lifecycleManager,
        // Additional dev tools
        debugConnections: () => {
          const stats = lifecycleManager.getStats();
          console.group('🔍 Supabase Connection Debug');
          console.log('Connection Stats:', stats);
          console.log(
            'Last Activity:',
            new Date(lifecycleManager.getLastActivity())
          );
          console.log('Is Online:', lifecycleManager.isOnline());
          console.log('Config:', lifecycleManager.getConfig());
          console.groupEnd();
        },
        forceInactivityCleanup: () => {
          console.log('🧹 Forcing inactivity cleanup...');
          lifecycleManager.forceCleanup('dev_tools_inactivity');
        },
        simulateOffline: () => {
          console.log('📡 Simulating offline event...');
          window.dispatchEvent(new Event('offline'));
        },
        simulateOnline: () => {
          console.log('📡 Simulating online event...');
          window.dispatchEvent(new Event('online'));
        },
        simulateVisibilityHidden: () => {
          console.log('👁️ Simulating visibility hidden...');
          Object.defineProperty(document, 'visibilityState', {
            writable: true,
            value: 'hidden',
          });
          document.dispatchEvent(new Event('visibilitychange'));
        },
        simulateVisibilityVisible: () => {
          console.log('👁️ Simulating visibility visible...');
          Object.defineProperty(document, 'visibilityState', {
            writable: true,
            value: 'visible',
          });
          document.dispatchEvent(new Event('visibilitychange'));
        },
      };

      // Log initialization
      console.log('🚀 Supabase Lifecycle Manager initialized');
      console.log('💡 Use window.supabaseLifecycle for debugging tools');
    }

    return () => {
      if (enableDevTools && typeof window !== 'undefined') {
        delete (window as any).supabaseLifecycle;
      }
    };
  }, [lifecycleManager, enableDevTools]);

  return (
    <SupabaseLifecycleContext.Provider value={lifecycleManager}>
      {children}
    </SupabaseLifecycleContext.Provider>
  );
}

/**
 * Hook to access the Supabase lifecycle manager
 *
 * @returns Lifecycle manager utilities
 */
export function useSupabaseLifecycle(): SupabaseLifecycleContextType {
  const context = useContext(SupabaseLifecycleContext);

  if (!context) {
    throw new Error(
      'useSupabaseLifecycle must be used within a SupabaseLifecycleProvider'
    );
  }

  return context;
}

/**
 * Higher-order component for adding lifecycle management to components
 *
 * @param Component - Component to wrap
 * @returns Wrapped component with lifecycle management
 */
export function withSupabaseLifecycle<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => {
    const lifecycle = useSupabaseLifecycle();

    return <Component {...props} supabaseLifecycle={lifecycle} />;
  };

  WrappedComponent.displayName = `withSupabaseLifecycle(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Component for displaying connection status (development only)
 */
export function SupabaseConnectionStatus() {
  const lifecycle = useSupabaseLifecycle();
  const [stats, setStats] = React.useState<any>(null);
  const [lastUpdate, setLastUpdate] = React.useState<Date>(new Date());

  // Update stats periodically
  React.useEffect(() => {
    const updateStats = () => {
      setStats(lifecycle.getStats());
      setLastUpdate(new Date());
    };

    updateStats();
    const interval = setInterval(updateStats, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [lifecycle]);

  // Only show in development
  if (process.env.NODE_ENV !== 'development' || !stats) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 10,
        right: 10,
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        maxWidth: '300px',
      }}
    >
      <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
        📡 Supabase Connections
      </div>
      <div>Total: {stats.total}</div>
      <div>Online: {lifecycle.isOnline() ? '✅' : '❌'}</div>
      <div>
        Last Activity:{' '}
        {Math.round((Date.now() - lifecycle.getLastActivity()) / 1000)}s ago
      </div>
      <div style={{ fontSize: '10px', opacity: 0.7, marginTop: '4px' }}>
        Updated: {lastUpdate.toLocaleTimeString()}
      </div>
      {stats.total > 0 && (
        <div style={{ marginTop: '4px' }}>
          <div style={{ fontSize: '10px', opacity: 0.7 }}>By Table:</div>
          {Object.entries(stats.byTable).map(([table, count]) => (
            <div key={table} style={{ fontSize: '10px' }}>
              {table}: {count as number}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Hook for components that need to register cleanup handlers
 *
 * @param handler - Cleanup function to register
 */
export function useSupabaseCleanupHandler(handler: () => void) {
  const lifecycle = useSupabaseLifecycle();

  React.useEffect(() => {
    const unregister = lifecycle.addCleanupHandler(handler);
    return unregister;
  }, [lifecycle, handler]);
}

/**
 * Hook for components that need to track activity
 *
 * This hook automatically resets the inactivity timer when the component
 * performs certain actions, helping to keep connections alive during active use.
 */
export function useSupabaseActivityTracker() {
  const lifecycle = useSupabaseLifecycle();

  const trackActivity = React.useCallback(() => {
    lifecycle.resetInactivityTimer();
  }, [lifecycle]);

  return { trackActivity };
}

/**
 * Development-only component for testing lifecycle events
 */
export function SupabaseLifecycleDebugPanel() {
  const lifecycle = useSupabaseLifecycle();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        bottom: 10,
        right: 10,
        background: 'rgba(0, 0, 0, 0.9)',
        color: 'white',
        padding: '12px',
        borderRadius: '6px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
      }}
    >
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
        🛠️ Lifecycle Debug
      </div>
      <button
        onClick={() => lifecycle.forceCleanup('debug_panel')}
        style={{
          background: '#ff4444',
          color: 'white',
          border: 'none',
          padding: '4px 8px',
          borderRadius: '3px',
          fontSize: '11px',
          cursor: 'pointer',
          marginRight: '4px',
          marginBottom: '4px',
        }}
      >
        Force Cleanup
      </button>
      <button
        onClick={() => lifecycle.resetInactivityTimer()}
        style={{
          background: '#44ff44',
          color: 'black',
          border: 'none',
          padding: '4px 8px',
          borderRadius: '3px',
          fontSize: '11px',
          cursor: 'pointer',
          marginRight: '4px',
          marginBottom: '4px',
        }}
      >
        Reset Timer
      </button>
      <button
        onClick={() => {
          const stats = lifecycle.getStats();
          console.log('📊 Connection Stats:', stats);
        }}
        style={{
          background: '#4444ff',
          color: 'white',
          border: 'none',
          padding: '4px 8px',
          borderRadius: '3px',
          fontSize: '11px',
          cursor: 'pointer',
          marginBottom: '4px',
        }}
      >
        Log Stats
      </button>
    </div>
  );
}
