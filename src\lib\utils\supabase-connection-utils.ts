/**
 * Supabase Connection Management Utilities
 *
 * Utility functions for managing Supabase real-time connections,
 * generating consistent channel keys, and handling connection lifecycle.
 *
 * <AUTHOR> Augster
 * @version 1.0 - Initial Implementation (January 2025)
 */

import type { SupabaseClient } from '@supabase/supabase-js';
import type { RealtimeChannel } from '@supabase/supabase-js';

// Channel configuration interface
export interface ChannelConfig {
  table: string;
  schema?: string;
  filter?: string;
  event?: '*' | 'INSERT' | 'UPDATE' | 'DELETE';
  tenantId?: string;
  componentName?: string;
}

// Channel key generation options
export interface ChannelKeyOptions {
  includeTenant?: boolean;
  includeFilter?: boolean;
  includeEvent?: boolean;
  customSuffix?: string;
}

/**
 * Generates a consistent channel key for subscription deduplication
 *
 * @param config - Channel configuration
 * @param options - Key generation options
 * @returns Unique channel key string
 */
export function generateChannelKey(
  config: ChannelConfig,
  options: ChannelKeyOptions = {}
): string {
  const {
    includeTenant = true,
    includeFilter = true,
    includeEvent = false,
    customSuffix,
  } = options;

  const parts: string[] = [];

  // Base table name
  parts.push(config.table);

  // Add tenant ID for multi-tenant isolation
  if (includeTenant && config.tenantId) {
    parts.push(config.tenantId);
  }

  // Add filter for query-specific subscriptions
  if (includeFilter && config.filter) {
    // Normalize filter string for consistent keys
    const normalizedFilter = config.filter
      .replace(/\s+/g, '') // Remove whitespace
      .replace(/[^a-zA-Z0-9=.,_-]/g, '_'); // Replace special chars
    parts.push(normalizedFilter);
  }

  // Add event type if specified
  if (includeEvent && config.event && config.event !== '*') {
    parts.push(config.event.toLowerCase());
  }

  // Add custom suffix if provided
  if (customSuffix) {
    parts.push(customSuffix);
  }

  return parts.join(':');
}

/**
 * Generates a human-readable channel name for Supabase
 *
 * @param config - Channel configuration
 * @returns Channel name string
 */
export function generateChannelName(config: ChannelConfig): string {
  const parts: string[] = ['realtime'];

  // Add table name
  parts.push(config.table);

  // Add tenant for isolation
  if (config.tenantId) {
    parts.push(config.tenantId);
  }

  // Add timestamp to ensure uniqueness if needed
  const timestamp = Date.now().toString(36);
  parts.push(timestamp);

  return parts.join('-');
}

/**
 * Validates channel configuration for common issues
 *
 * @param config - Channel configuration to validate
 * @returns Validation result with errors if any
 */
export function validateChannelConfig(config: ChannelConfig): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!config.table || typeof config.table !== 'string') {
    errors.push('Table name is required and must be a string');
  }

  // Table name validation
  if (config.table && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(config.table)) {
    errors.push('Table name contains invalid characters');
  }

  // Schema validation
  if (config.schema && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(config.schema)) {
    errors.push('Schema name contains invalid characters');
  }

  // Filter validation
  if (config.filter) {
    // Check for common filter issues
    if (config.filter.includes('tenant_id=eq.') && !config.tenantId) {
      warnings.push('Filter contains tenant_id but tenantId is not specified');
    }

    if (config.filter.length > 500) {
      warnings.push(
        'Filter string is very long and may cause performance issues'
      );
    }
  }

  // Tenant ID validation
  if (config.tenantId) {
    // Check if it looks like a UUID
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(config.tenantId)) {
      warnings.push('Tenant ID does not appear to be a valid UUID');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Creates a standardized Supabase channel with proper configuration
 *
 * @param supabase - Supabase client instance
 * @param config - Channel configuration
 * @returns Configured RealtimeChannel
 */
export function createStandardizedChannel(
  supabase: SupabaseClient,
  config: ChannelConfig
): RealtimeChannel {
  // Validate configuration
  const validation = validateChannelConfig(config);
  if (!validation.isValid) {
    throw new Error(`Invalid channel config: ${validation.errors.join(', ')}`);
  }

  // Log warnings in development
  if (
    process.env.NODE_ENV === 'development' &&
    validation.warnings.length > 0
  ) {
    console.warn('Channel config warnings:', validation.warnings);
  }

  // Generate channel name
  const channelName = generateChannelName(config);

  // Create channel with standardized configuration
  const channel = supabase.channel(channelName);

  // Configure postgres_changes listener
  return channel.on(
    'postgres_changes',
    {
      event: config.event || '*',
      schema: config.schema || 'public',
      table: config.table,
      filter: config.filter,
    },
    // Placeholder handler - will be replaced by actual implementation
    () => {}
  );
}

/**
 * Extracts tenant ID from various filter formats
 *
 * @param filter - Filter string to parse
 * @returns Extracted tenant ID or null
 */
export function extractTenantIdFromFilter(filter?: string): string | null {
  if (!filter) return null;

  // Common patterns for tenant_id filters
  const patterns = [
    /tenant_id=eq\.([^&\s]+)/,
    /tenant_id\.eq\.([^&\s]+)/,
    /tenant_id\s*=\s*'([^']+)'/,
    /tenant_id\s*=\s*"([^"]+)"/,
  ];

  for (const pattern of patterns) {
    const match = filter.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

/**
 * Normalizes filter strings for consistent comparison
 *
 * @param filter - Filter string to normalize
 * @returns Normalized filter string
 */
export function normalizeFilter(filter?: string): string {
  if (!filter) return '';

  return filter
    .trim()
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/\s*=\s*/g, '=') // Remove spaces around equals
    .replace(/\s*\.\s*/g, '.') // Remove spaces around dots
    .toLowerCase();
}

/**
 * Checks if two channel configurations are equivalent
 *
 * @param config1 - First configuration
 * @param config2 - Second configuration
 * @returns True if configurations are equivalent
 */
export function areChannelConfigsEquivalent(
  config1: ChannelConfig,
  config2: ChannelConfig
): boolean {
  return (
    config1.table === config2.table &&
    (config1.schema || 'public') === (config2.schema || 'public') &&
    normalizeFilter(config1.filter) === normalizeFilter(config2.filter) &&
    (config1.event || '*') === (config2.event || '*') &&
    config1.tenantId === config2.tenantId
  );
}

/**
 * Generates a hash of channel configuration for quick comparison
 *
 * @param config - Channel configuration
 * @returns Configuration hash string
 */
export function hashChannelConfig(config: ChannelConfig): string {
  const normalized = {
    table: config.table,
    schema: config.schema || 'public',
    filter: normalizeFilter(config.filter),
    event: config.event || '*',
    tenantId: config.tenantId || '',
  };

  // Simple hash function for configuration
  const str = JSON.stringify(normalized);
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36);
}

/**
 * Connection health check utilities
 */
export const connectionHealth = {
  /**
   * Checks if a channel is still active and responsive
   */
  isChannelHealthy: (channel: RealtimeChannel): boolean => {
    try {
      // Check channel state
      return channel.state === 'joined' || channel.state === 'joining';
    } catch {
      return false;
    }
  },

  /**
   * Attempts to ping a channel to verify connectivity
   */
  pingChannel: async (channel: RealtimeChannel): Promise<boolean> => {
    try {
      // Send a presence update as a connectivity test
      const result = await channel.track({ ping: Date.now() });
      return result === 'ok';
    } catch {
      return false;
    }
  },

  /**
   * Gets channel connection statistics
   */
  getChannelStats: (channel: RealtimeChannel) => {
    try {
      return {
        state: channel.state,
        topic: channel.topic,
        joinRef: channel.joinRef,
        // Add more stats as needed
      };
    } catch {
      return null;
    }
  },
};

/**
 * Development utilities for debugging connections
 */
export const debugUtils = {
  /**
   * Logs detailed channel information
   */
  logChannelInfo: (channel: RealtimeChannel, label?: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔍 Channel Debug${label ? ` - ${label}` : ''}`);
      console.log('State:', channel.state);
      console.log('Topic:', channel.topic);
      console.log('Join Ref:', channel.joinRef);
      console.log('Health:', connectionHealth.isChannelHealthy(channel));
      console.groupEnd();
    }
  },

  /**
   * Monitors channel state changes
   */
  monitorChannelState: (channel: RealtimeChannel, label?: string) => {
    if (process.env.NODE_ENV === 'development') {
      const originalState = channel.state;

      // Set up a simple state monitor
      const checkState = () => {
        if (channel.state !== originalState) {
          console.log(
            `📡 Channel state changed${label ? ` (${label})` : ''}:`,
            {
              from: originalState,
              to: channel.state,
              timestamp: new Date().toISOString(),
            }
          );
        }
      };

      // Check state periodically
      const interval = setInterval(checkState, 1000);

      // Clean up after 30 seconds
      setTimeout(() => clearInterval(interval), 30000);
    }
  },
};
