/**
 * Supabase Connection Debugging Tools
 * 
 * Comprehensive debugging utilities for Supabase real-time connections.
 * Provides detailed analysis, leak detection, and performance insights.
 * 
 * <AUTHOR> Augster
 * @version 1.0 - Initial Implementation (January 2025)
 */

import { useSupabaseStore } from '@/stores/supabase-store';
import { connectionMonitor, monitoringUtils } from '@/lib/utils/connection-monitor';

// Debug report interface
interface DebugReport {
  timestamp: number;
  summary: {
    totalConnections: number;
    healthyConnections: number;
    staleConnections: number;
    duplicateGroups: number;
    memoryUsage?: string;
  };
  connections: Array<{
    key: string;
    table: string;
    tenantId?: string;
    filter?: string;
    age: string;
    refCount: number;
    lastActivity: string;
    isStale: boolean;
  }>;
  issues: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    affectedConnections?: string[];
    recommendation?: string;
  }>;
  performance: {
    averageAge: string;
    oldestConnection: string;
    peakConnections: number;
    connectionsByTable: Record<string, number>;
    connectionsByTenant: Record<string, number>;
  };
}

/**
 * Generates a comprehensive debug report for Supabase connections
 */
export function generateDebugReport(): DebugReport {
  const store = useSupabaseStore.getState();
  const stats = store.getConnectionStats();
  const channels = store.channels;
  const now = Date.now();
  
  // Analyze connections
  const connections = Object.entries(channels).map(([key, managedChannel]) => {
    const age = now - managedChannel.metadata.createdAt;
    const lastActivity = now - managedChannel.metadata.lastActivity;
    const isStale = lastActivity > 30 * 60 * 1000; // 30 minutes
    
    return {
      key,
      table: managedChannel.metadata.table,
      tenantId: managedChannel.metadata.tenantId,
      filter: managedChannel.metadata.filter,
      age: formatDuration(age),
      refCount: managedChannel.refCount,
      lastActivity: formatDuration(lastActivity),
      isStale,
    };
  });
  
  // Detect issues
  const issues: DebugReport['issues'] = [];
  
  // Check for stale connections
  const staleConnections = connections.filter(c => c.isStale);
  if (staleConnections.length > 0) {
    issues.push({
      type: 'warning',
      message: `Found ${staleConnections.length} stale connections (inactive > 30 minutes)`,
      affectedConnections: staleConnections.map(c => c.key),
      recommendation: 'Consider calling removeInactiveChannels() or reducing inactivity timeout',
    });
  }
  
  // Check for high connection count
  if (stats.total > 100) {
    issues.push({
      type: 'warning',
      message: `High connection count: ${stats.total}`,
      recommendation: 'Review connection usage and implement proper cleanup',
    });
  }
  
  if (stats.total > 150) {
    issues.push({
      type: 'error',
      message: `Critical connection count: ${stats.total} (approaching Supabase limit of 200)`,
      recommendation: 'Immediate action required: clean up connections or increase limits',
    });
  }
  
  // Check for duplicate connections
  const duplicateGroups = findDuplicateConnections(channels);
  if (duplicateGroups.length > 0) {
    issues.push({
      type: 'warning',
      message: `Found ${duplicateGroups.length} groups of potentially duplicate connections`,
      affectedConnections: duplicateGroups.flat(),
      recommendation: 'Review channel key generation and deduplication logic',
    });
  }
  
  // Check for high reference counts (potential memory leaks)
  const highRefCountConnections = connections.filter(c => c.refCount > 5);
  if (highRefCountConnections.length > 0) {
    issues.push({
      type: 'warning',
      message: `Found ${highRefCountConnections.length} connections with high reference counts`,
      affectedConnections: highRefCountConnections.map(c => c.key),
      recommendation: 'Check for component unmount issues or excessive subscription sharing',
    });
  }
  
  // Performance analysis
  const ages = connections.map(c => now - managedChannel.metadata.createdAt);
  const averageAge = ages.length > 0 ? ages.reduce((sum, age) => sum + age, 0) / ages.length : 0;
  const oldestAge = ages.length > 0 ? Math.max(...ages) : 0;
  
  // Memory usage (if available)
  let memoryUsage: string | undefined;
  if (typeof window !== 'undefined' && 'performance' in window && 'memory' in window.performance) {
    // @ts-ignore - memory is not in standard types
    const memory = window.performance.memory;
    if (memory) {
      memoryUsage = `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`;
    }
  }
  
  return {
    timestamp: now,
    summary: {
      totalConnections: stats.total,
      healthyConnections: connections.filter(c => !c.isStale).length,
      staleConnections: staleConnections.length,
      duplicateGroups: duplicateGroups.length,
      memoryUsage,
    },
    connections,
    issues,
    performance: {
      averageAge: formatDuration(averageAge),
      oldestConnection: formatDuration(oldestAge),
      peakConnections: monitoringUtils.getStats().metrics.peakConnections,
      connectionsByTable: stats.byTable,
      connectionsByTenant: stats.byTenant,
    },
  };
}

/**
 * Finds groups of potentially duplicate connections
 */
function findDuplicateConnections(channels: Record<string, any>): string[][] {
  const groups = new Map<string, string[]>();
  
  Object.entries(channels).forEach(([key, managedChannel]) => {
    const signature = `${managedChannel.metadata.table}:${managedChannel.metadata.filter || ''}:${managedChannel.metadata.tenantId || ''}`;
    
    if (!groups.has(signature)) {
      groups.set(signature, []);
    }
    groups.get(signature)!.push(key);
  });
  
  return Array.from(groups.values()).filter(group => group.length > 1);
}

/**
 * Formats duration in milliseconds to human-readable string
 */
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${Math.round(ms / 1000)}s`;
  if (ms < 3600000) return `${Math.round(ms / 60000)}m`;
  return `${Math.round(ms / 3600000)}h`;
}

/**
 * Prints a formatted debug report to console
 */
export function printDebugReport(report?: DebugReport): void {
  const debugReport = report || generateDebugReport();
  
  console.group('🔍 Supabase Connection Debug Report');
  console.log('Generated:', new Date(debugReport.timestamp).toLocaleString());
  
  // Summary
  console.group('📊 Summary');
  console.log('Total Connections:', debugReport.summary.totalConnections);
  console.log('Healthy Connections:', debugReport.summary.healthyConnections);
  console.log('Stale Connections:', debugReport.summary.staleConnections);
  console.log('Duplicate Groups:', debugReport.summary.duplicateGroups);
  if (debugReport.summary.memoryUsage) {
    console.log('Memory Usage:', debugReport.summary.memoryUsage);
  }
  console.groupEnd();
  
  // Issues
  if (debugReport.issues.length > 0) {
    console.group('⚠️ Issues');
    debugReport.issues.forEach(issue => {
      const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`${icon} ${issue.message}`);
      if (issue.recommendation) {
        console.log(`   💡 ${issue.recommendation}`);
      }
      if (issue.affectedConnections) {
        console.log(`   🎯 Affected: ${issue.affectedConnections.slice(0, 3).join(', ')}${issue.affectedConnections.length > 3 ? '...' : ''}`);
      }
    });
    console.groupEnd();
  }
  
  // Performance
  console.group('📈 Performance');
  console.log('Average Age:', debugReport.performance.averageAge);
  console.log('Oldest Connection:', debugReport.performance.oldestConnection);
  console.log('Peak Connections:', debugReport.performance.peakConnections);
  console.log('By Table:', debugReport.performance.connectionsByTable);
  if (Object.keys(debugReport.performance.connectionsByTenant).length > 0) {
    console.log('By Tenant:', debugReport.performance.connectionsByTenant);
  }
  console.groupEnd();
  
  // Connections (top 10)
  if (debugReport.connections.length > 0) {
    console.group('🔗 Connections (top 10)');
    debugReport.connections
      .sort((a, b) => (b.isStale ? 1 : 0) - (a.isStale ? 1 : 0))
      .slice(0, 10)
      .forEach(conn => {
        const status = conn.isStale ? '🔴' : '🟢';
        console.log(`${status} ${conn.table} (${conn.age}, refs: ${conn.refCount})`);
        if (conn.tenantId) console.log(`   Tenant: ${conn.tenantId}`);
        if (conn.filter) console.log(`   Filter: ${conn.filter}`);
      });
    console.groupEnd();
  }
  
  console.groupEnd();
}

/**
 * Analyzes connection patterns and provides recommendations
 */
export function analyzeConnectionPatterns(): {
  patterns: Array<{
    pattern: string;
    count: number;
    recommendation: string;
  }>;
  recommendations: string[];
} {
  const store = useSupabaseStore.getState();
  const channels = store.channels;
  
  const patterns: Record<string, number> = {};
  const tableUsage: Record<string, number> = {};
  const tenantUsage: Record<string, number> = {};
  
  Object.values(channels).forEach(managedChannel => {
    const { table, tenantId, filter } = managedChannel.metadata;
    
    // Track table usage
    tableUsage[table] = (tableUsage[table] || 0) + 1;
    
    // Track tenant usage
    if (tenantId) {
      tenantUsage[tenantId] = (tenantUsage[tenantId] || 0) + 1;
    }
    
    // Track patterns
    const pattern = filter ? `${table}:filtered` : `${table}:unfiltered`;
    patterns[pattern] = (patterns[pattern] || 0) + 1;
  });
  
  const recommendations: string[] = [];
  
  // Analyze patterns
  const patternAnalysis = Object.entries(patterns).map(([pattern, count]) => {
    let recommendation = '';
    
    if (pattern.includes(':unfiltered') && count > 1) {
      recommendation = 'Consider adding filters to reduce data transfer';
    } else if (pattern.includes(':filtered') && count > 3) {
      recommendation = 'Multiple filtered subscriptions - ensure filters are necessary';
    }
    
    return { pattern, count, recommendation };
  });
  
  // General recommendations
  if (Object.keys(tableUsage).length > 10) {
    recommendations.push('High number of different tables - consider consolidating subscriptions');
  }
  
  if (Object.keys(tenantUsage).length > 5) {
    recommendations.push('Multiple tenants active - ensure proper tenant isolation');
  }
  
  const totalConnections = Object.keys(channels).length;
  if (totalConnections > 50) {
    recommendations.push('High connection count - implement connection pooling or batching');
  }
  
  return {
    patterns: patternAnalysis,
    recommendations,
  };
}

/**
 * Development-only global debugging utilities
 */
export const debugTools = {
  /**
   * Generate and print debug report
   */
  debug: () => printDebugReport(),
  
  /**
   * Get raw debug report data
   */
  getReport: () => generateDebugReport(),
  
  /**
   * Analyze connection patterns
   */
  analyze: () => analyzeConnectionPatterns(),
  
  /**
   * Force cleanup of all connections
   */
  cleanup: () => {
    const store = useSupabaseStore.getState();
    store.removeAllChannels();
    console.log('🧹 All connections cleaned up');
  },
  
  /**
   * Remove inactive connections
   */
  cleanupInactive: (threshold = 5 * 60 * 1000) => {
    const store = useSupabaseStore.getState();
    const removed = store.removeInactiveChannels(threshold);
    console.log(`🧹 Removed ${removed} inactive connections`);
  },
  
  /**
   * Get monitoring statistics
   */
  getStats: () => monitoringUtils.getStats(),
  
  /**
   * Generate monitoring report
   */
  getMonitoringReport: () => monitoringUtils.generateReport(),
  
  /**
   * Start/stop monitoring
   */
  startMonitoring: () => monitoringUtils.start(),
  stopMonitoring: () => monitoringUtils.stop(),
};

// Make debug tools available globally in development
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  (window as any).supabaseDebug = debugTools;
  console.log('🛠️ Supabase debug tools available at window.supabaseDebug');
}
